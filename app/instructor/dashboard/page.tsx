"use client"

import * as React from "react"
import { motion } from "framer-motion"
import { 
  BookOpen, 
  Users, 
  TrendingUp, 
  Plus,
  Play,
  MessageSquare,
  Star,
  DollarSign,
  ArrowUpRight,
  Calendar,
  Clock,
  Award,
  Target
} from "lucide-react"
import { ProtectedRoute } from '@/components/auth/protected-route'
import { InstructorSidebar } from '@/components/instructor/sidebar'
import { InstructorHeader } from '@/components/instructor/header'
import { UserGrowthChart, ActivityChart } from '@/components/admin/charts'
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useAuth } from '@/contexts/auth-context'
import Link from "next/link"

// Mock data for charts
const enrollmentData = [
  { name: 'Jan', value: 45 },
  { name: 'Feb', value: 52 },
  { name: '<PERSON>', value: 48 },
  { name: 'Apr', value: 61 },
  { name: 'May', value: 55 },
  { name: 'Jun', value: 67 },
]

const revenueData = [
  { name: '<PERSON>', value: 120 },
  { name: 'Tue', value: 190 },
  { name: 'Wed', value: 300 },
  { name: 'Thu', value: 250 },
  { name: 'Fri', value: 400 },
  { name: 'Sat', value: 200 },
  { name: 'Sun', value: 150 },
]

export default function InstructorDashboard() {
  const [sidebarCollapsed, setSidebarCollapsed] = React.useState(false)
  const { userProfile } = useAuth()

  const stats = [
    {
      title: "My Courses",
      value: "12",
      change: "+3 this month",
      changeType: "positive" as const,
      icon: BookOpen,
      color: "from-blue-500 to-blue-600",
      bgColor: "bg-blue-50",
      iconColor: "text-blue-600"
    },
    {
      title: "Total Students",
      value: "456",
      change: "+23 new enrollments",
      changeType: "positive" as const,
      icon: Users,
      color: "from-green-500 to-green-600",
      bgColor: "bg-green-50",
      iconColor: "text-green-600"
    },
    {
      title: "Course Rating",
      value: "4.8",
      change: "Average rating",
      changeType: "positive" as const,
      icon: Star,
      color: "from-yellow-500 to-yellow-600",
      bgColor: "bg-yellow-50",
      iconColor: "text-yellow-600"
    },
    {
      title: "Monthly Revenue",
      value: "$3,240",
      change: "+18.2% from last month",
      changeType: "positive" as const,
      icon: DollarSign,
      color: "from-purple-500 to-purple-600",
      bgColor: "bg-purple-50",
      iconColor: "text-purple-600"
    }
  ]

  const quickActions = [
    {
      title: "Create Course",
      description: "Start building a new course",
      icon: Plus,
      href: "/instructor/courses/create",
      color: "bg-primary",
      textColor: "text-white"
    },
    {
      title: "Record Lecture",
      description: "Add new video content",
      icon: Play,
      href: "#",
      color: "bg-blue-50",
      textColor: "text-blue-600"
    },
    {
      title: "View Messages",
      description: "Check student inquiries",
      icon: MessageSquare,
      href: "#",
      color: "bg-green-50",
      textColor: "text-green-600"
    },
    {
      title: "Analytics",
      description: "View performance metrics",
      icon: TrendingUp,
      href: "/instructor/analytics",
      color: "bg-purple-50",
      textColor: "text-purple-600"
    }
  ]

  const recentCourses = [
    {
      id: 1,
      title: "React Fundamentals",
      students: 89,
      rating: 4.9,
      status: "published",
      lastUpdated: "2 days ago",
      progress: 85,
      thumbnail: "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=100&h=100&fit=crop&crop=center"
    },
    {
      id: 2,
      title: "Advanced JavaScript",
      students: 67,
      rating: 4.7,
      status: "published",
      lastUpdated: "1 week ago",
      progress: 92,
      thumbnail: "https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=100&h=100&fit=crop&crop=center"
    },
    {
      id: 3,
      title: "Node.js Masterclass",
      students: 0,
      rating: 0,
      status: "draft",
      lastUpdated: "3 days ago",
      progress: 45,
      thumbnail: "https://images.unsplash.com/photo-1618477247222-acbdb0e159b3?w=100&h=100&fit=crop&crop=center"
    }
  ]

  const recentStudents = [
    {
      id: 1,
      name: "Sarah Johnson",
      email: "<EMAIL>",
      course: "React Fundamentals",
      enrolledAt: "2 hours ago",
      avatar: "https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=150",
      progress: 75
    },
    {
      id: 2,
      name: "Michael Chen",
      email: "<EMAIL>",
      course: "Advanced JavaScript",
      enrolledAt: "5 hours ago",
      avatar: "https://images.pexels.com/photos/1040880/pexels-photo-1040880.jpeg?auto=compress&cs=tinysrgb&w=150",
      progress: 45
    },
    {
      id: 3,
      name: "Emily Davis",
      email: "<EMAIL>",
      course: "React Fundamentals",
      enrolledAt: "1 day ago",
      avatar: "https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=150",
      progress: 90
    }
  ]

  return (
    <ProtectedRoute allowedRoles={[2]}>
      <div className="flex h-screen bg-background">
        <InstructorSidebar 
          collapsed={sidebarCollapsed} 
          onToggle={() => setSidebarCollapsed(!sidebarCollapsed)} 
        />
        
        <div className="flex-1 flex flex-col overflow-hidden">
          <InstructorHeader 
            title="Dashboard"
            subtitle={`Welcome back, ${userProfile?.name || 'Instructor'}! Here's what's happening with your courses.`}
          />
          
          <main className="flex-1 overflow-y-auto bg-background">
            <div className="container-modern space-y-8">
              {/* Welcome Section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-3xl p-8 text-white relative overflow-hidden shadow-2xl"
              >
                <div className="relative z-10">
                  <h2 className="text-4xl font-bold mb-3 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
                    Good morning, {userProfile?.name || 'Instructor'}! 👋
                  </h2>
                  <p className="text-blue-100 text-lg mb-8 leading-relaxed">
                    You have 3 new student enrollments and 2 pending course reviews. Your courses are performing excellently!
                  </p>
                  <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                    <Button variant="secondary" className="bg-white/95 text-blue-700 hover:bg-white shadow-lg border-0 font-semibold">
                      <Calendar className="h-4 w-4 mr-2" />
                      Schedule Meeting
                    </Button>
                    <Button variant="ghost" className="text-white border-white/30 hover:bg-white/15 backdrop-blur-sm">
                      View All Activities
                      <ArrowUpRight className="h-4 w-4 ml-2" />
                    </Button>
                  </div>
                </div>
                <div className="absolute top-0 right-0 w-80 h-80 bg-gradient-to-br from-white/20 to-transparent rounded-full -translate-y-40 translate-x-40"></div>
                <div className="absolute bottom-0 right-0 w-40 h-40 bg-gradient-to-tl from-white/10 to-transparent rounded-full translate-y-20 translate-x-20"></div>
                <div className="absolute top-1/2 left-0 w-32 h-32 bg-gradient-to-r from-white/5 to-transparent rounded-full -translate-x-16"></div>
              </motion.div>

              {/* Stats Cards */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
              >
                {stats.map((stat, index) => (
                  <motion.div
                    key={stat.title}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.1 + index * 0.1 }}
                  >
                    <Card className="stats-card border-0 shadow-sm hover:shadow-md transition-all duration-200">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between mb-4">
                          <div className={`w-12 h-12 rounded-xl ${stat.bgColor} flex items-center justify-center`}>
                            <stat.icon className={`h-6 w-6 ${stat.iconColor}`} />
                          </div>
                          <ArrowUpRight className="h-4 w-4 text-muted-foreground" />
                        </div>
                        <div>
                          <p className="text-2xl font-bold text-foreground mb-1">{stat.value}</p>
                          <p className="text-sm font-medium text-foreground mb-2">{stat.title}</p>
                          <p className="text-xs text-muted-foreground">{stat.change}</p>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </motion.div>

              {/* Quick Actions */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <Card className="border-0 shadow-sm">
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-xl font-semibold">Quick Actions</CardTitle>
                        <CardDescription className="text-muted-foreground">
                          Get started with common tasks
                        </CardDescription>
                      </div>
                      <Target className="h-5 w-5 text-muted-foreground" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      {quickActions.map((action, index) => (
                        <motion.div
                          key={action.title}
                          initial={{ opacity: 0, scale: 0.95 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ duration: 0.3, delay: 0.3 + index * 0.1 }}
                        >
                          <Link href={action.href}>
                            <Card className="card-hover cursor-pointer border-0 shadow-sm">
                              <CardContent className="p-6">
                                <div className={`w-12 h-12 rounded-xl ${action.color} flex items-center justify-center mb-4`}>
                                  <action.icon className={`h-6 w-6 ${action.textColor}`} />
                                </div>
                                <h3 className="font-semibold text-foreground mb-2">{action.title}</h3>
                                <p className="text-sm text-muted-foreground">{action.description}</p>
                              </CardContent>
                            </Card>
                          </Link>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Charts */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="grid grid-cols-1 lg:grid-cols-2 gap-6"
              >
                <UserGrowthChart
                  title="Student Enrollments"
                  description="Monthly enrollment trends"
                  data={enrollmentData}
                />
                <ActivityChart
                  title="Weekly Revenue"
                  description="Revenue over the past week"
                  data={revenueData}
                />
              </motion.div>

              {/* Recent Activity */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
                className="grid grid-cols-1 lg:grid-cols-2 gap-6"
              >
                {/* Recent Courses */}
                <Card className="border-0 shadow-sm">
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-xl font-semibold">Recent Courses</CardTitle>
                        <CardDescription className="text-muted-foreground">
                          Your latest course activity
                        </CardDescription>
                      </div>
                      <BookOpen className="h-5 w-5 text-muted-foreground" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {recentCourses.map((course, index) => (
                        <motion.div
                          key={course.id}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.3, delay: 0.5 + index * 0.1 }}
                          className="flex items-center space-x-4 p-4 rounded-xl border border-border hover:bg-muted/30 transition-colors duration-200 cursor-pointer"
                        >
                          <div className="w-16 h-16 rounded-lg overflow-hidden bg-muted flex-shrink-0">
                            <img 
                              src={course.thumbnail} 
                              alt={course.title}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between mb-1">
                              <h4 className="font-semibold text-foreground truncate">{course.title}</h4>
                              {course.rating > 0 && (
                                <div className="flex items-center space-x-1">
                                  <Star className="h-4 w-4 text-yellow-500 fill-current" />
                                  <span className="text-sm font-medium">{course.rating}</span>
                                </div>
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground mb-2">
                              {course.students} students • Updated {course.lastUpdated}
                            </p>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                <Badge 
                                  variant={course.status === 'published' ? 'default' : 'secondary'}
                                  className={course.status === 'published' ? 'badge-success' : 'badge-neutral'}
                                >
                                  {course.status}
                                </Badge>
                                <span className="text-xs text-muted-foreground">{course.progress}% complete</span>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Recent Students */}
                <Card className="border-0 shadow-sm">
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-xl font-semibold">Recent Students</CardTitle>
                        <CardDescription className="text-muted-foreground">
                          Latest student enrollments
                        </CardDescription>
                      </div>
                      <Users className="h-5 w-5 text-muted-foreground" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {recentStudents.map((student, index) => (
                        <motion.div
                          key={student.id}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.3, delay: 0.5 + index * 0.1 }}
                          className="flex items-center space-x-4 p-4 rounded-xl border border-border hover:bg-muted/30 transition-colors duration-200 cursor-pointer"
                        >
                          <Avatar className="h-12 w-12 border-2 border-border">
                            <AvatarImage src={student.avatar} alt={student.name} />
                            <AvatarFallback className="bg-primary text-primary-foreground font-semibold">
                              {student.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between mb-1">
                              <h4 className="font-semibold text-foreground truncate">{student.name}</h4>
                              <div className="flex items-center space-x-1 text-muted-foreground">
                                <Clock className="h-3 w-3" />
                                <span className="text-xs">{student.enrolledAt}</span>
                              </div>
                            </div>
                            <p className="text-sm text-muted-foreground mb-2 truncate">
                              Enrolled in {student.course}
                            </p>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                <div className="w-16 h-1.5 bg-muted rounded-full overflow-hidden">
                                  <div 
                                    className="h-full bg-primary rounded-full transition-all duration-300"
                                    style={{ width: `${student.progress}%` }}
                                  />
                                </div>
                                <span className="text-xs text-muted-foreground">{student.progress}%</span>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </main>
        </div>
      </div>
    </ProtectedRoute>
  )
}