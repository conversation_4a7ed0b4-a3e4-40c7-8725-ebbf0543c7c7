"use client"

import * as React from "react"
import { motion } from "framer-motion"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { 
  <PERSON>Left, 
  Save, 
  Plus, 
  Trash2, 
  Eye,
  Clock,
  Target,
  HelpCircle
} from "lucide-react"
import { ProtectedRoute } from '@/components/auth/protected-route'
import { InstructorSidebar } from '@/components/instructor/sidebar'
import { InstructorHeader } from '@/components/instructor/header'
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { toast } from "react-hot-toast"
import { instructor<PERSON><PERSON>, <PERSON>uizQuest<PERSON> } from '@/lib/instructor-api'
import { useAuth } from '@/contexts/auth-context'
import Link from "next/link"

interface QuizFormData {
  title: string
  course_id: string
  time_limit: string
  passing_score: string
  questions: QuizQuestion[]
}

interface QuestionFormData {
  question_text: string
  type: 'multiple_choice' | 'true_false' | 'short_answer'
  options: string[]
  correct_answer: string
  points: string
}

export default function CreateQuizPage() {
  const [sidebarCollapsed, setSidebarCollapsed] = React.useState(false)
  const [loading, setLoading] = React.useState(false)
  const [courses, setCourses] = React.useState<any[]>([])
  const [currentQuestionIndex, setCurrentQuestionIndex] = React.useState(0)
  
  const router = useRouter()
  const { userProfile } = useAuth()

  const [quizData, setQuizData] = React.useState<QuizFormData>({
    title: '',
    course_id: '',
    time_limit: '',
    passing_score: '',
    questions: []
  })

  const [currentQuestion, setCurrentQuestion] = React.useState<QuestionFormData>({
    question_text: '',
    type: 'multiple_choice',
    options: ['', '', '', ''],
    correct_answer: '',
    points: '1'
  })

  // Fetch instructor's courses
  React.useEffect(() => {
    const fetchCourses = async () => {
      if (!userProfile?.id) return
      
      try {
        const response = await instructorApi.getCoursesByInstructor(userProfile.id)
        if (response.success) {
          setCourses(response.data)
        }
      } catch (error) {
        console.error('Error fetching courses:', error)
      }
    }

    fetchCourses()
  }, [userProfile])

  const handleQuizDataChange = (field: keyof QuizFormData, value: string) => {
    setQuizData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleQuestionChange = (field: keyof QuestionFormData, value: string | string[]) => {
    setCurrentQuestion(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleOptionChange = (index: number, value: string) => {
    const newOptions = [...currentQuestion.options]
    newOptions[index] = value
    setCurrentQuestion(prev => ({
      ...prev,
      options: newOptions
    }))
  }

  const addOption = () => {
    if (currentQuestion.options.length < 6) {
      setCurrentQuestion(prev => ({
        ...prev,
        options: [...prev.options, '']
      }))
    }
  }

  const removeOption = (index: number) => {
    if (currentQuestion.options.length > 2) {
      const newOptions = currentQuestion.options.filter((_, i) => i !== index)
      setCurrentQuestion(prev => ({
        ...prev,
        options: newOptions,
        correct_answer: prev.correct_answer === prev.options[index] ? '' : prev.correct_answer
      }))
    }
  }

  const addQuestion = () => {
    if (!currentQuestion.question_text.trim()) {
      toast.error('Question text is required')
      return
    }

    if (!currentQuestion.correct_answer.trim()) {
      toast.error('Correct answer is required')
      return
    }

    if (currentQuestion.type === 'multiple_choice' && currentQuestion.options.filter(opt => opt.trim()).length < 2) {
      toast.error('At least 2 options are required for multiple choice questions')
      return
    }

    const newQuestion: QuizQuestion = {
      id: Date.now(), // Temporary ID
      quiz_id: 0, // Will be set when quiz is created
      question_text: currentQuestion.question_text,
      type: currentQuestion.type,
      options: currentQuestion.type === 'multiple_choice' ? currentQuestion.options.filter(opt => opt.trim()) : undefined,
      correct_answer: currentQuestion.correct_answer,
      points: parseInt(currentQuestion.points) || 1
    }

    setQuizData(prev => ({
      ...prev,
      questions: [...prev.questions, newQuestion]
    }))

    // Reset current question
    setCurrentQuestion({
      question_text: '',
      type: 'multiple_choice',
      options: ['', '', '', ''],
      correct_answer: '',
      points: '1'
    })

    toast.success('Question added successfully')
  }

  const editQuestion = (index: number) => {
    const question = quizData.questions[index]
    setCurrentQuestion({
      question_text: question.question_text,
      type: question.type,
      options: question.options || ['', '', '', ''],
      correct_answer: question.correct_answer,
      points: question.points?.toString() || '1'
    })
    setCurrentQuestionIndex(index)
    removeQuestion(index)
  }

  const removeQuestion = (index: number) => {
    setQuizData(prev => ({
      ...prev,
      questions: prev.questions.filter((_, i) => i !== index)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!quizData.title.trim()) {
      toast.error('Quiz title is required')
      return
    }

    if (!quizData.course_id) {
      toast.error('Please select a course')
      return
    }

    if (quizData.questions.length === 0) {
      toast.error('At least one question is required')
      return
    }

    try {
      setLoading(true)
      
      // Create quiz first
      const quizPayload = {
        title: quizData.title,
        course_id: parseInt(quizData.course_id),
        time_limit: quizData.time_limit ? parseInt(quizData.time_limit) : undefined,
        passing_score: quizData.passing_score ? parseInt(quizData.passing_score) : undefined
      }

      const quizResponse = await instructorApi.createQuiz(quizPayload)
      
      if (quizResponse.success) {
        const quizId = quizResponse.data.id

        // Add questions to the quiz
        for (const question of quizData.questions) {
          const questionPayload = {
            quiz_id: quizId,
            question_text: question.question_text,
            type: question.type,
            options: question.options,
            correct_answer: question.correct_answer,
            points: question.points
          }

          await instructorApi.addQuizQuestion(questionPayload)
        }

        toast.success('Quiz created successfully!')
        router.push('/instructor/assessments')
      } else {
        toast.error(quizResponse.message || 'Failed to create quiz')
      }
    } catch (error) {
      console.error('Error creating quiz:', error)
      toast.error('Failed to create quiz')
    } finally {
      setLoading(false)
    }
  }

  const getTotalPoints = () => {
    return quizData.questions.reduce((total, q) => total + (q.points || 1), 0)
  }

  return (
    <ProtectedRoute allowedRoles={[2]}>
      <div className="flex h-screen bg-background">
        <InstructorSidebar 
          collapsed={sidebarCollapsed} 
          onToggle={() => setSidebarCollapsed(!sidebarCollapsed)} 
        />
        
        <div className="flex-1 flex flex-col overflow-hidden">
          <InstructorHeader 
            title="Create Quiz"
            subtitle="Build an interactive quiz for your students"
          />
          
          <main className="flex-1 overflow-y-auto p-6">
            <div className="max-w-6xl mx-auto space-y-6">
              {/* Back Button */}
              <div className="flex items-center space-x-4">
                <Button variant="ghost" asChild>
                  <Link href="/instructor/assessments">
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back to Assessments
                  </Link>
                </Button>
              </div>

              <form onSubmit={handleSubmit}>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Main Content */}
                  <div className="lg:col-span-2 space-y-6">
                    {/* Quiz Information */}
                    <Card>
                      <CardHeader>
                        <CardTitle>Quiz Information</CardTitle>
                        <CardDescription>
                          Basic information about your quiz
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="title">Quiz Title *</Label>
                          <Input
                            id="title"
                            placeholder="Enter quiz title"
                            value={quizData.title}
                            onChange={(e) => handleQuizDataChange('title', e.target.value)}
                            required
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="course">Course *</Label>
                          <Select
                            value={quizData.course_id}
                            onValueChange={(value) => handleQuizDataChange('course_id', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select a course" />
                            </SelectTrigger>
                            <SelectContent>
                              {courses.map((course) => (
                                <SelectItem key={course.id} value={course.id.toString()}>
                                  {course.title}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="timeLimit">Time Limit (minutes)</Label>
                            <Input
                              id="timeLimit"
                              type="number"
                              min="1"
                              placeholder="30"
                              value={quizData.time_limit}
                              onChange={(e) => handleQuizDataChange('time_limit', e.target.value)}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="passingScore">Passing Score (%)</Label>
                            <Input
                              id="passingScore"
                              type="number"
                              min="0"
                              max="100"
                              placeholder="70"
                              value={quizData.passing_score}
                              onChange={(e) => handleQuizDataChange('passing_score', e.target.value)}
                            />
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Add Question */}
                    <Card>
                      <CardHeader>
                        <CardTitle>Add Question</CardTitle>
                        <CardDescription>
                          Create a new question for your quiz
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="questionText">Question *</Label>
                          <Textarea
                            id="questionText"
                            placeholder="Enter your question"
                            rows={3}
                            value={currentQuestion.question_text}
                            onChange={(e) => handleQuestionChange('question_text', e.target.value)}
                          />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="questionType">Question Type</Label>
                            <Select
                              value={currentQuestion.type}
                              onValueChange={(value: any) => handleQuestionChange('type', value)}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="multiple_choice">Multiple Choice</SelectItem>
                                <SelectItem value="true_false">True/False</SelectItem>
                                <SelectItem value="short_answer">Short Answer</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="points">Points</Label>
                            <Input
                              id="points"
                              type="number"
                              min="1"
                              value={currentQuestion.points}
                              onChange={(e) => handleQuestionChange('points', e.target.value)}
                            />
                          </div>
                        </div>

                        {/* Options for Multiple Choice */}
                        {currentQuestion.type === 'multiple_choice' && (
                          <div className="space-y-4">
                            <div className="flex items-center justify-between">
                              <Label>Answer Options</Label>
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={addOption}
                                disabled={currentQuestion.options.length >= 6}
                              >
                                <Plus className="mr-2 h-4 w-4" />
                                Add Option
                              </Button>
                            </div>
                            
                            <div className="space-y-2">
                              {currentQuestion.options.map((option, index) => (
                                <div key={index} className="flex items-center space-x-2">
                                  <Input
                                    placeholder={`Option ${index + 1}`}
                                    value={option}
                                    onChange={(e) => handleOptionChange(index, e.target.value)}
                                  />
                                  <Button
                                    type="button"
                                    variant="outline"
                                    size="icon"
                                    onClick={() => removeOption(index)}
                                    disabled={currentQuestion.options.length <= 2}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              ))}
                            </div>

                            <div className="space-y-2">
                              <Label>Correct Answer</Label>
                              <Select
                                value={currentQuestion.correct_answer}
                                onValueChange={(value) => handleQuestionChange('correct_answer', value)}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Select correct answer" />
                                </SelectTrigger>
                                <SelectContent>
                                  {currentQuestion.options
                                    .filter(opt => opt.trim())
                                    .map((option, index) => (
                                    <SelectItem key={index} value={option}>
                                      {option}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        )}

                        {/* True/False Options */}
                        {currentQuestion.type === 'true_false' && (
                          <div className="space-y-2">
                            <Label>Correct Answer</Label>
                            <Select
                              value={currentQuestion.correct_answer}
                              onValueChange={(value) => handleQuestionChange('correct_answer', value)}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select correct answer" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="True">True</SelectItem>
                                <SelectItem value="False">False</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        )}

                        {/* Short Answer */}
                        {currentQuestion.type === 'short_answer' && (
                          <div className="space-y-2">
                            <Label htmlFor="correctAnswer">Correct Answer</Label>
                            <Input
                              id="correctAnswer"
                              placeholder="Enter the correct answer"
                              value={currentQuestion.correct_answer}
                              onChange={(e) => handleQuestionChange('correct_answer', e.target.value)}
                            />
                          </div>
                        )}

                        <Button type="button" onClick={addQuestion}>
                          <Plus className="mr-2 h-4 w-4" />
                          Add Question
                        </Button>
                      </CardContent>
                    </Card>

                    {/* Questions List */}
                    {quizData.questions.length > 0 && (
                      <Card>
                        <CardHeader>
                          <CardTitle>Questions ({quizData.questions.length})</CardTitle>
                          <CardDescription>
                            Review and manage your quiz questions
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          {quizData.questions.map((question, index) => (
                            <div key={question.id} className="border rounded-lg p-4 space-y-2">
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <div className="flex items-center space-x-2 mb-2">
                                    <Badge variant="outline">{question.type.replace('_', ' ')}</Badge>
                                    <Badge variant="secondary">{question.points} point{question.points !== 1 ? 's' : ''}</Badge>
                                  </div>
                                  <p className="font-medium">{question.question_text}</p>
                                  {question.options && (
                                    <div className="mt-2 space-y-1">
                                      {question.options.map((option, optIndex) => (
                                        <div key={optIndex} className="flex items-center space-x-2">
                                          <div className={`w-2 h-2 rounded-full ${
                                            option === question.correct_answer ? 'bg-green-500' : 'bg-gray-300'
                                          }`} />
                                          <span className={option === question.correct_answer ? 'font-medium text-green-700' : ''}>
                                            {option}
                                          </span>
                                        </div>
                                      ))}
                                    </div>
                                  )}
                                  {!question.options && (
                                    <p className="text-sm text-green-700 font-medium mt-2">
                                      Correct answer: {question.correct_answer}
                                    </p>
                                  )}
                                </div>
                                <div className="flex items-center space-x-2">
                                  <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={() => editQuestion(index)}
                                  >
                                    Edit
                                  </Button>
                                  <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={() => removeQuestion(index)}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </CardContent>
                      </Card>
                    )}
                  </div>

                  {/* Sidebar */}
                  <div className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle>Quiz Summary</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="flex items-center space-x-2">
                          <HelpCircle className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">
                            {quizData.questions.length} question{quizData.questions.length !== 1 ? 's' : ''}
                          </span>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Target className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">
                            {getTotalPoints()} total point{getTotalPoints() !== 1 ? 's' : ''}
                          </span>
                        </div>

                        {quizData.time_limit && (
                          <div className="flex items-center space-x-2">
                            <Clock className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">
                              {quizData.time_limit} minute{parseInt(quizData.time_limit) !== 1 ? 's' : ''} time limit
                            </span>
                          </div>
                        )}

                        <Separator />

                        <Button
                          type="submit"
                          className="w-full"
                          disabled={loading || quizData.questions.length === 0}
                        >
                          <Save className="mr-2 h-4 w-4" />
                          Create Quiz
                        </Button>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </form>
            </div>
          </main>
        </div>
      </div>
    </ProtectedRoute>
  )
}