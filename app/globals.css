@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer base {
  :root {
    /* Premium Light Mode Design System */
    --background: 250 251 252; /* #FAFBFC - Ultra light background */
    --foreground: 15 23 42; /* #0F172A - Rich dark text */
    --card: 255 255 255; /* #FFFFFF - Pure white cards */
    --card-foreground: 15 23 42; /* #0F172A - Rich dark text */
    --popover: 255 255 255; /* #FFFFFF */
    --popover-foreground: 15 23 42; /* #0F172A */

    /* Role-based Primary Colors */
    --primary: 16 185 129; /* #10B981 - Student Green */
    --primary-foreground: 255 255 255; /* #FFFFFF */
    --primary-light: 167 243 208; /* #A7F3D0 */
    --primary-dark: 5 150 105; /* #059669 */

    /* Admin Colors */
    --admin-primary: 99 102 241; /* #6366F1 - Indigo */
    --admin-secondary: 139 92 246; /* #8B5CF6 - Purple */
    --admin-light: 199 210 254; /* #C7D2FE */

    /* Instructor Colors */
    --instructor-primary: 239 68 68; /* #EF4444 - Red */
    --instructor-secondary: 249 115 22; /* #F97316 - Orange */
    --instructor-light: 254 202 202; /* #FECACA */

    /* Neutral Colors */
    --secondary: 248 250 252; /* #F8FAFC */
    --secondary-foreground: 51 65 85; /* #334155 */
    --muted: 241 245 249; /* #F1F5F9 */
    --muted-foreground: 100 116 139; /* #64748B */
    --accent: 14 165 233; /* #0EA5E9 - Sky blue */
    --accent-foreground: 255 255 255; /* #FFFFFF */

    /* Status Colors */
    --destructive: 239 68 68; /* #EF4444 */
    --destructive-foreground: 255 255 255; /* #FFFFFF */
    --success: 34 197 94; /* #22C55E */
    --warning: 245 158 11; /* #F59E0B */
    --info: 59 130 246; /* #3B82F6 */

    /* UI Elements */
    --border: 226 232 240; /* #E2E8F0 */
    --input: 241 245 249; /* #F1F5F9 */
    --ring: 16 185 129; /* #10B981 */
    --radius: 0.75rem; /* 12px */

    /* Chart Colors */
    --chart-1: 16 185 129; /* #10B981 - Green */
    --chart-2: 59 130 246; /* #3B82F6 - Blue */
    --chart-3: 245 158 11; /* #F59E0B - Orange */
    --chart-4: 139 92 246; /* #8B5CF6 - Purple */
    --chart-5: 236 72 153; /* #EC4899 - Pink */
  }

  .dark {
    /* Premium Dark Mode Design System */
    --background: 2 6 23; /* #020617 - Deep dark background */
    --foreground: 248 250 252; /* #F8FAFC - Soft white text */
    --card: 15 23 42; /* #0F172A - Rich dark cards */
    --card-foreground: 248 250 252; /* #F8FAFC - Soft white text */
    --popover: 15 23 42; /* #0F172A */
    --popover-foreground: 248 250 252; /* #F8FAFC */

    /* Role-based Primary Colors (Enhanced for dark) */
    --primary: 34 197 94; /* #22C55E - Brighter green for dark */
    --primary-foreground: 2 6 23; /* #020617 - Dark text on green */
    --primary-light: 74 222 128; /* #4ADE80 */
    --primary-dark: 21 128 61; /* #15803D */

    /* Admin Colors (Dark Mode) */
    --admin-primary: 129 140 248; /* #818CF8 - Lighter indigo */
    --admin-secondary: 167 139 250; /* #A78BFA - Lighter purple */
    --admin-light: 99 102 241; /* #6366F1 */

    /* Instructor Colors (Dark Mode) */
    --instructor-primary: 248 113 113; /* #F87171 - Lighter red */
    --instructor-secondary: 251 146 60; /* #FB923C - Lighter orange */
    --instructor-light: 239 68 68; /* #EF4444 */

    /* Neutral Colors (Dark Mode) */
    --secondary: 30 41 59; /* #1E293B */
    --secondary-foreground: 203 213 225; /* #CBD5E1 */
    --muted: 30 41 59; /* #1E293B */
    --muted-foreground: 148 163 184; /* #94A3B8 */
    --accent: 56 189 248; /* #38BDF8 - Brighter sky blue */
    --accent-foreground: 2 6 23; /* #020617 */

    /* Status Colors (Dark Mode) */
    --destructive: 248 113 113; /* #F87171 */
    --destructive-foreground: 2 6 23; /* #020617 */
    --success: 74 222 128; /* #4ADE80 */
    --warning: 251 191 36; /* #FBBF24 */
    --info: 96 165 250; /* #60A5FA */

    /* UI Elements (Dark Mode) */
    --border: 51 65 85; /* #334155 */
    --input: 30 41 59; /* #1E293B */
    --ring: 34 197 94; /* #22C55E */

    /* Chart Colors (Dark Mode - Enhanced) */
    --chart-1: 74 222 128; /* #4ADE80 - Bright green */
    --chart-2: 96 165 250; /* #60A5FA - Bright blue */
    --chart-3: 251 191 36; /* #FBBF24 - Bright orange */
    --chart-4: 167 139 250; /* #A78BFA - Bright purple */
    --chart-5: 244 114 182; /* #F472B6 - Bright pink */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }
}

@layer utilities {
  /* Premium Glass Effect */
  .glass {
    @apply backdrop-blur-xl bg-white/80 dark:bg-slate-900/80 border border-white/20 dark:border-slate-700/50;
  }

  .glass-card {
    @apply backdrop-blur-xl bg-white/90 dark:bg-slate-900/90 border border-white/20 dark:border-slate-700/50 shadow-xl;
  }

  /* Premium Gradients */
  .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary-dark)) 100%);
  }

  .gradient-admin {
    background: linear-gradient(135deg, hsl(var(--admin-primary)) 0%, hsl(var(--admin-secondary)) 100%);
  }

  .gradient-instructor {
    background: linear-gradient(135deg, hsl(var(--instructor-primary)) 0%, hsl(var(--instructor-secondary)) 100%);
  }

  .gradient-card {
    background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--muted)) 100%);
  }

  .gradient-dark {
    background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--card)) 100%);
  }

  /* Premium Shadows */
  .shadow-premium {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .shadow-glow {
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
  }

  .shadow-admin-glow {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  }

  .shadow-instructor-glow {
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
  }

  /* Modern Cards */
  .card-premium {
    @apply bg-card border border-border rounded-2xl shadow-lg hover:shadow-premium transition-all duration-300 hover:-translate-y-1;
  }

  .card-glass {
    @apply glass-card rounded-2xl shadow-xl hover:shadow-premium transition-all duration-300 hover:-translate-y-1;
  }

  .card-gradient {
    @apply gradient-card border border-border rounded-2xl shadow-lg hover:shadow-premium transition-all duration-300 hover:-translate-y-1;
  }

  /* Interactive Elements */
  .btn-premium {
    @apply px-6 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 active:scale-95;
  }

  .btn-gradient {
    @apply btn-premium gradient-primary text-white shadow-lg hover:shadow-glow;
  }

  .btn-glass {
    @apply btn-premium glass text-foreground hover:bg-white/20 dark:hover:bg-slate-800/20;
  }

  /* Animations */
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.4s ease-out;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  /* Role-based Backgrounds */
  .bg-student {
    @apply bg-gradient-to-br from-emerald-50 to-teal-50 dark:from-emerald-950/20 dark:to-teal-950/20;
  }

  .bg-admin {
    @apply bg-gradient-to-br from-indigo-50 to-purple-50 dark:from-indigo-950/20 dark:to-purple-950/20;
  }

  .bg-instructor {
    @apply bg-gradient-to-br from-red-50 to-orange-50 dark:from-red-950/20 dark:to-orange-950/20;
  }

  /* Text Gradients */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-emerald-600 to-teal-600 dark:from-emerald-400 dark:to-teal-400 bg-clip-text text-transparent;
  }

  .text-gradient-admin {
    @apply bg-gradient-to-r from-indigo-600 to-purple-600 dark:from-indigo-400 dark:to-purple-400 bg-clip-text text-transparent;
  }

  .text-gradient-instructor {
    @apply bg-gradient-to-r from-red-600 to-orange-600 dark:from-red-400 dark:to-orange-400 bg-clip-text text-transparent;
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
  }
  to {
    box-shadow: 0 0 30px rgba(16, 185, 129, 0.6);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }
  
  /* Design System Typography */
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 600;
    line-height: 1.25;
  }
  
  /* Smooth transitions for interactive elements */
  button, a, [role="button"] {
    transition: all 0.2s ease-in-out;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
  }
  
  ::-webkit-scrollbar-track {
    background: #F1F3F4;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #E8EAED;
    border-radius: 3px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: #6B7280;
  }
}
