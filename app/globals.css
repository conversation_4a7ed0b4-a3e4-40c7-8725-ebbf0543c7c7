@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer base {
  :root {
    /* Design System Colors */
    --background: 248 249 250; /* #F8F9FA */
    --foreground: 31 41 55; /* #1F2937 */
    --card: 255 255 255; /* #FFFFFF */
    --card-foreground: 31 41 55; /* #1F2937 */
    --popover: 255 255 255; /* #FFFFFF */
    --popover-foreground: 31 41 55; /* #1F2937 */
    --primary: 255 75 75; /* #FF4B4B */
    --primary-foreground: 255 255 255; /* #FFFFFF */
    --secondary: 241 243 244; /* #F1F3F4 */
    --secondary-foreground: 31 41 55; /* #1F2937 */
    --muted: 241 243 244; /* #F1F3F4 */
    --muted-foreground: 107 114 128; /* #6B7280 */
    --accent: 59 130 246; /* #3B82F6 */
    --accent-foreground: 255 255 255; /* #FFFFFF */
    --destructive: 239 68 68; /* #EF4444 */
    --destructive-foreground: 255 255 255; /* #FFFFFF */
    --border: 232 234 237; /* #E8EAED */
    --input: 232 234 237; /* #E8EAED */
    --ring: 255 75 75; /* #FF4B4B */
    --chart-1: 59 130 246; /* #3B82F6 */
    --chart-2: 16 185 129; /* #10B981 */
    --chart-3: 245 158 11; /* #F59E0B */
    --chart-4: 139 92 246; /* #8B5CF6 */
    --chart-5: 255 107 107; /* #FF6B6B */
    --radius: 0.75rem; /* 12px */
  }
  .dark {
    --background: 31 41 55; /* #1F2937 */
    --foreground: 248 249 250; /* #F8F9FA */
    --card: 55 65 81; /* #374151 */
    --card-foreground: 248 249 250; /* #F8F9FA */
    --popover: 55 65 81; /* #374151 */
    --popover-foreground: 248 249 250; /* #F8F9FA */
    --primary: 255 75 75; /* #FF4B4B */
    --primary-foreground: 255 255 255; /* #FFFFFF */
    --secondary: 75 85 99; /* #4B5563 */
    --secondary-foreground: 248 249 250; /* #F8F9FA */
    --muted: 75 85 99; /* #4B5563 */
    --muted-foreground: 156 163 175; /* #9CA3AF */
    --accent: 59 130 246; /* #3B82F6 */
    --accent-foreground: 255 255 255; /* #FFFFFF */
    --destructive: 239 68 68; /* #EF4444 */
    --destructive-foreground: 255 255 255; /* #FFFFFF */
    --border: 75 85 99; /* #4B5563 */
    --input: 75 85 99; /* #4B5563 */
    --ring: 255 75 75; /* #FF4B4B */
    --chart-1: 59 130 246; /* #3B82F6 */
    --chart-2: 16 185 129; /* #10B981 */
    --chart-3: 245 158 11; /* #F59E0B */
    --chart-4: 139 92 246; /* #8B5CF6 */
    --chart-5: 255 107 107; /* #FF6B6B */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }
  
  /* Design System Typography */
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 600;
    line-height: 1.25;
  }
  
  /* Smooth transitions for interactive elements */
  button, a, [role="button"] {
    transition: all 0.2s ease-in-out;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
  }
  
  ::-webkit-scrollbar-track {
    background: #F1F3F4;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #E8EAED;
    border-radius: 3px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: #6B7280;
  }
}
