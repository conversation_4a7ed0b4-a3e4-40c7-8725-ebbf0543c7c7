@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer base {
  :root {
    /* Design System Colors - Light Mode */
    --background: 248 249 250; /* #F8F9FA */
    --foreground: 0 0 0; /* #000000 - Pure black for text */
    --card: 255 255 255; /* #FFFFFF */
    --card-foreground: 0 0 0; /* #000000 - Pure black for card text */
    --popover: 255 255 255; /* #FFFFFF */
    --popover-foreground: 0 0 0; /* #000000 */
    --primary: 255 75 75; /* #FF4B4B */
    --primary-foreground: 255 255 255; /* #FFFFFF */
    --secondary: 241 243 244; /* #F1F3F4 */
    --secondary-foreground: 0 0 0; /* #000000 */
    --muted: 241 243 244; /* #F1F3F4 */
    --muted-foreground: 107 114 128; /* #6B7280 */
    --accent: 59 130 246; /* #3B82F6 */
    --accent-foreground: 255 255 255; /* #FFFFFF */
    --destructive: 239 68 68; /* #EF4444 */
    --destructive-foreground: 255 255 255; /* #FFFFFF */
    --border: 232 234 237; /* #E8EAED */
    --input: 232 234 237; /* #E8EAED */
    --ring: 255 75 75; /* #FF4B4B */
    --chart-1: 59 130 246; /* #3B82F6 */
    --chart-2: 16 185 129; /* #10B981 */
    --chart-3: 245 158 11; /* #F59E0B */
    --chart-4: 139 92 246; /* #8B5CF6 */
    --chart-5: 255 107 107; /* #FF6B6B */
    --radius: 0.75rem; /* 12px */
  }
  .dark {
    /* Design System Colors - Dark Mode */
    --background: 17 24 39; /* #111827 - Darker background */
    --foreground: 255 255 255; /* #FFFFFF - Pure white for text */
    --card: 31 41 55; /* #1F2937 - Darker card background */
    --card-foreground: 255 255 255; /* #FFFFFF - Pure white for card text */
    --popover: 31 41 55; /* #1F2937 */
    --popover-foreground: 255 255 255; /* #FFFFFF */
    --primary: 255 75 75; /* #FF4B4B */
    --primary-foreground: 255 255 255; /* #FFFFFF */
    --secondary: 55 65 81; /* #374151 */
    --secondary-foreground: 255 255 255; /* #FFFFFF */
    --muted: 55 65 81; /* #374151 */
    --muted-foreground: 156 163 175; /* #9CA3AF */
    --accent: 59 130 246; /* #3B82F6 */
    --accent-foreground: 255 255 255; /* #FFFFFF */
    --destructive: 239 68 68; /* #EF4444 */
    --destructive-foreground: 255 255 255; /* #FFFFFF */
    --border: 55 65 81; /* #374151 */
    --input: 55 65 81; /* #374151 */
    --ring: 255 75 75; /* #FF4B4B */
    --chart-1: 59 130 246; /* #3B82F6 */
    --chart-2: 16 185 129; /* #10B981 */
    --chart-3: 245 158 11; /* #F59E0B */
    --chart-4: 139 92 246; /* #8B5CF6 */
    --chart-5: 255 107 107; /* #FF6B6B */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer utilities {
  /* Typography utilities */
  .text-primary-black {
    @apply text-black dark:text-white;
  }

  .text-secondary-gray {
    @apply text-gray-600 dark:text-gray-400;
  }

  .text-muted-gray {
    @apply text-gray-500 dark:text-gray-500;
  }

  /* Card utilities */
  .card-modern {
    @apply bg-white dark:bg-gray-800 border-0 shadow-sm rounded-2xl;
  }

  .card-hover {
    @apply hover:shadow-md transition-all duration-200 hover:-translate-y-1;
  }

  /* Background utilities */
  .bg-dashboard {
    @apply bg-gray-50 dark:bg-gray-900;
  }

  .bg-surface {
    @apply bg-white dark:bg-gray-800;
  }

  .bg-surface-secondary {
    @apply bg-gray-50 dark:bg-gray-700;
  }

  /* Border utilities */
  .border-modern {
    @apply border border-gray-200 dark:border-gray-700;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }
  
  /* Design System Typography */
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 600;
    line-height: 1.25;
  }
  
  /* Smooth transitions for interactive elements */
  button, a, [role="button"] {
    transition: all 0.2s ease-in-out;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
  }
  
  ::-webkit-scrollbar-track {
    background: #F1F3F4;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #E8EAED;
    border-radius: 3px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: #6B7280;
  }
}
