{"designSystem": {"name": "Modern Freelance Dashboard", "version": "1.0.0", "description": "A clean, modern dashboard design system for freelance management platforms", "colorPalette": {"primary": {"brand": "#FF4B4B", "brandSecondary": "#FF6B6B"}, "neutrals": {"background": "#F8F9FA", "surface": "#FFFFFF", "surfaceSecondary": "#F1F3F4", "border": "#E8EAED", "textPrimary": "#1F2937", "textSecondary": "#6B7280", "textMuted": "#9CA3AF"}, "accents": {"blue": "#3B82F6", "green": "#10B981", "orange": "#F59E0B", "purple": "#8B5CF6"}, "status": {"paid": "#10B981", "notPaid": "#6B7280", "warning": "#F59E0B", "error": "#EF4444"}}, "typography": {"fontFamily": {"primary": "Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "fallback": "system-ui, sans-serif"}, "fontSizes": {"xs": "0.75rem", "sm": "0.875rem", "base": "1rem", "lg": "1.125rem", "xl": "1.25rem", "2xl": "1.5rem", "3xl": "1.875rem", "4xl": "2.25rem"}, "fontWeights": {"normal": 400, "medium": 500, "semibold": 600, "bold": 700}, "lineHeights": {"tight": 1.25, "normal": 1.5, "relaxed": 1.75}}, "spacing": {"micro": "0.25rem", "xs": "0.5rem", "sm": "0.75rem", "md": "1rem", "lg": "1.5rem", "xl": "2rem", "2xl": "2.5rem", "3xl": "3rem", "4xl": "4rem"}, "borderRadius": {"sm": "0.375rem", "md": "0.5rem", "lg": "0.75rem", "xl": "1rem", "2xl": "1.5rem", "full": "9999px"}, "shadows": {"sm": "0 1px 2px 0 rgba(0, 0, 0, 0.05)", "md": "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)", "lg": "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)", "xl": "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"}, "components": {"layout": {"container": {"maxWidth": "1200px", "padding": "2rem", "margin": "0 auto", "background": "#F8F9FA"}, "grid": {"columns": 12, "gap": "1.5rem", "breakpoints": {"sm": "640px", "md": "768px", "lg": "1024px", "xl": "1280px"}}}, "navigation": {"header": {"height": "4rem", "background": "#FFFFFF", "borderBottom": "1px solid #E8EAED", "padding": "0 2rem", "display": "flex", "alignItems": "center", "justifyContent": "space-between"}, "logo": {"display": "flex", "alignItems": "center", "gap": "0.5rem", "fontSize": "1.25rem", "fontWeight": 600}, "navItems": {"display": "flex", "gap": "2rem", "fontSize": "0.875rem", "fontWeight": 500, "color": "#6B7280"}, "searchBar": {"width": "320px", "height": "2.5rem", "borderRadius": "0.5rem", "border": "1px solid #E8EAED", "padding": "0 1rem", "fontSize": "0.875rem"}}, "cards": {"primary": {"background": "#FFFFFF", "borderRadius": "1rem", "padding": "1.5rem", "boxShadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "border": "1px solid #E8EAED"}, "secondary": {"background": "#F8F9FA", "borderRadius": "0.75rem", "padding": "1rem", "border": "1px solid #E8EAED"}, "hover": {"transform": "translateY(-2px)", "boxShadow": "0 10px 15px -3px rgba(0, 0, 0, 0.1)"}}, "buttons": {"primary": {"background": "#FF4B4B", "color": "#FFFFFF", "borderRadius": "0.5rem", "padding": "0.75rem 1.5rem", "fontSize": "0.875rem", "fontWeight": 600, "border": "none", "cursor": "pointer"}, "secondary": {"background": "#F1F3F4", "color": "#1F2937", "borderRadius": "0.5rem", "padding": "0.75rem 1.5rem", "fontSize": "0.875rem", "fontWeight": 500, "border": "1px solid #E8EAED"}, "ghost": {"background": "transparent", "color": "#6B7280", "borderRadius": "0.375rem", "padding": "0.5rem 1rem", "fontSize": "0.875rem", "fontWeight": 500, "border": "none"}}, "badges": {"status": {"paid": {"background": "#10B981", "color": "#FFFFFF", "borderRadius": "0.375rem", "padding": "0.25rem 0.75rem", "fontSize": "0.75rem", "fontWeight": 600}, "notPaid": {"background": "#6B7280", "color": "#FFFFFF", "borderRadius": "0.375rem", "padding": "0.25rem 0.75rem", "fontSize": "0.75rem", "fontWeight": 600}, "remote": {"background": "#E5E7EB", "color": "#1F2937", "borderRadius": "0.375rem", "padding": "0.25rem 0.75rem", "fontSize": "0.75rem", "fontWeight": 500}}}, "charts": {"lineChart": {"strokeWidth": "2px", "pointRadius": "4px", "gridColor": "#E8EAED", "primaryColor": "#3B82F6", "backgroundColor": "#F8F9FA"}, "barChart": {"barRadius": "0.25rem", "spacing": "0.5rem", "colors": ["#3B82F6", "#10B981", "#F59E0B", "#EF4444"]}}, "forms": {"input": {"height": "2.5rem", "borderRadius": "0.5rem", "border": "1px solid #E8EAED", "padding": "0 1rem", "fontSize": "0.875rem", "background": "#FFFFFF"}, "select": {"height": "2.5rem", "borderRadius": "0.5rem", "border": "1px solid #E8EAED", "padding": "0 1rem", "fontSize": "0.875rem", "background": "#FFFFFF"}}, "avatars": {"small": {"width": "2rem", "height": "2rem", "borderRadius": "50%", "border": "2px solid #FFFFFF"}, "medium": {"width": "2.5rem", "height": "2.5rem", "borderRadius": "50%", "border": "2px solid #FFFFFF"}, "large": {"width": "3rem", "height": "3rem", "borderRadius": "50%", "border": "2px solid #FFFFFF"}}}, "patterns": {"dashboard": {"layout": "grid", "columns": "repeat(auto-fit, minmax(300px, 1fr))", "gap": "1.5rem", "padding": "2rem"}, "statsCard": {"structure": {"header": {"display": "flex", "alignItems": "center", "justifyContent": "space-between", "marginBottom": "1rem"}, "metric": {"fontSize": "2.25rem", "fontWeight": 700, "lineHeight": 1.2, "marginBottom": "0.5rem"}, "description": {"fontSize": "0.875rem", "color": "#6B7280", "lineHeight": 1.5}}}, "projectCard": {"structure": {"header": {"display": "flex", "alignItems": "flex-start", "gap": "1rem", "marginBottom": "1rem"}, "icon": {"width": "2.5rem", "height": "2.5rem", "borderRadius": "0.5rem", "display": "flex", "alignItems": "center", "justifyContent": "center"}, "content": {"flex": 1}, "title": {"fontSize": "1rem", "fontWeight": 600, "marginBottom": "0.25rem"}, "meta": {"fontSize": "0.875rem", "color": "#6B7280", "marginBottom": "0.75rem"}, "tags": {"display": "flex", "gap": "0.5rem", "marginBottom": "0.75rem"}}}, "listItem": {"structure": {"container": {"display": "flex", "alignItems": "center", "gap": "1rem", "padding": "1rem", "borderRadius": "0.5rem", "background": "#FFFFFF", "border": "1px solid #E8EAED"}, "avatar": {"flexShrink": 0}, "content": {"flex": 1, "minWidth": 0}, "action": {"flexShrink": 0}}}}, "interactions": {"hover": {"cards": "transform: translateY(-2px); box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);", "buttons": "opacity: 0.9; transform: translateY(-1px);", "links": "color: #FF4B4B;"}, "focus": {"inputs": "outline: 2px solid #FF4B4B; outline-offset: 2px;", "buttons": "outline: 2px solid #FF4B4B; outline-offset: 2px;"}, "active": {"buttons": "transform: translateY(0px); box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);"}}, "responsive": {"breakpoints": {"mobile": "max-width: 640px", "tablet": "max-width: 768px", "desktop": "min-width: 1024px"}, "adaptations": {"mobile": {"navigation": "collapse to hamburger menu", "grid": "single column layout", "cards": "full width with reduced padding", "spacing": "reduce padding and margins by 25%"}, "tablet": {"navigation": "condensed horizontal layout", "grid": "2-column layout", "cards": "maintain padding", "spacing": "standard spacing"}}}}}